// TrueBDC CRM Automation Suite - Simple Auto Refresh with Timer and Modal
// EXACT COPY of working Tampermonkey script adapted for Chrome extension

class AutoRefresh {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;

        this.init();
    }

    init() {
        const frameContext = TrueBDCUtils.getFrameContext();
        console.log('[TrueBDC AutoRefresh] Initializing...', frameContext);
        console.log('[TrueBDC AutoRefresh] Current URL:', window.location.href);

        // Check if we're on a supported page
        if (this.isSupportedPage()) {
            console.log('[TrueBDC AutoRefresh] Supported page detected, starting script');
            this.startScript();
            this.isActive = true;

            TrueBDCUtils.logActivity('auto_refresh_activated', {
                url: window.location.href,
                frameContext: frameContext
            });
        } else {
            console.log('[TrueBDC AutoRefresh] Not a supported page');
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        console.log('[TrueBDC AutoRefresh] Full URL:', url);
        console.log('[TrueBDC AutoRefresh] Hostname:', window.location.hostname);
        console.log('[TrueBDC AutoRefresh] Pathname:', window.location.pathname);

        // EXACT URL matching as specified: https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/weblink/weblinkToday.aspx*
        const isSupported = url.includes('eleadcrm.com') &&
                           url.includes('/elead_track/weblink/weblinkToday.aspx');
        console.log('[TrueBDC AutoRefresh] URL check - Supported:', isSupported);
        console.log('[TrueBDC AutoRefresh] Looking for: /elead_track/weblink/weblinkToday.aspx');
        return isSupported;
    }

    startScript() {
        console.log('[TrueBDC AutoRefresh] Starting the exact working script...');

        // EXACT COPY of the working Tampermonkey script
        (function() {
            'use strict';

            /** @type {number} */
            let intervalId = null;
            /** @type {boolean} */
            let isPaused = false;
            /** @type {number} */
            let countdown = 0;
            /** @type {number} */
            let refreshTime = Number(localStorage.getItem('autoRefreshTime')) || 5;

            console.log('[TrueBDC AutoRefresh] Script variables initialized:', { intervalId, isPaused, countdown, refreshTime });

            /**
             * TIMER DISPLAY (Progress Bar Style) - Create or update the timer display in the corner.
             */
            function createTimerDisplay() {
                console.log('[TrueBDC AutoRefresh] Creating timer display...');
                let timer = document.getElementById('autoRefreshTimerDisplay');
                if (!timer) {
                    console.log('[TrueBDC AutoRefresh] Timer element not found, creating new one');
                    timer = document.createElement('div');
                    timer.id = 'autoRefreshTimerDisplay';
                    timer.style.cssText = `
                        position: fixed;
                        bottom: 20px;
                        left: 20px;
                        background: #fff;
                        color: #333;
                        padding: 12px 20px;
                        border-radius: 8px;
                        font-family: sans-serif;
                        font-size: 14px;
                        cursor: pointer;
                        z-index: 10000;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                        position: relative;
                        overflow: hidden;
                        transition: all 0.3s ease;
                    `;
                    timer.title = 'Click to set refresh interval';
                    timer.addEventListener('click', showModal);

                    // Create progress bar
                    const progressBar = document.createElement('div');
                    progressBar.id = 'progressBar';
                    progressBar.style.cssText = `
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        height: 3px;
                        background: linear-gradient(90deg, #4CAF50, #45a049);
                        transition: width 1s linear;
                        width: 100%;
                    `;
                    timer.appendChild(progressBar);

                    // Add hover effect
                    timer.addEventListener('mouseenter', () => {
                        timer.style.transform = 'translateY(-2px)';
                        timer.style.boxShadow = '0 6px 20px rgba(0,0,0,0.15)';
                    });
                    timer.addEventListener('mouseleave', () => {
                        timer.style.transform = 'translateY(0px)';
                        timer.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
                    });

                    document.body.appendChild(timer);
                    console.log('[TrueBDC AutoRefresh] Timer element created and added to DOM');
                } else {
                    console.log('[TrueBDC AutoRefresh] Timer element found, updating content');
                }

                // Update text content
                const textNode = timer.firstChild && timer.firstChild.nodeType === Node.TEXT_NODE ?
                    timer.firstChild : document.createTextNode('');
                if (!timer.firstChild || timer.firstChild.nodeType !== Node.TEXT_NODE) {
                    timer.insertBefore(textNode, timer.firstChild);
                }
                textNode.textContent = isPaused ? `⏸️ Paused` : `🔄 Refresh in ${countdown}s`;
                timer.style.opacity = isPaused ? '0.5' : '1';

                // Update progress bar
                const progressBar = document.getElementById('progressBar');
                if (progressBar && !isPaused) {
                    const progressPercent = ((refreshTime - countdown) / refreshTime) * 100;
                    progressBar.style.width = `${progressPercent}%`;
                } else if (progressBar && isPaused) {
                    progressBar.style.width = '0%';
                }

                console.log('[TrueBDC AutoRefresh] Timer display updated:', textNode.textContent);
            }

            /**
             * SETTINGS MODAL (Fade Blur Style) - Show a modal to set the refresh interval.
             */
            function showModal() {
                console.log('[TrueBDC AutoRefresh] showModal called');
                if (document.getElementById('refreshTimeModal')) {
                    console.log('[TrueBDC AutoRefresh] Modal already exists, returning');
                    return;
                }
                isPaused = true;
                updateDisplay();

                // Create backdrop
                const backdrop = document.createElement('div');
                backdrop.id = 'modalBackdrop';
                backdrop.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.3);
                    z-index: 10000;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                `;
                document.body.appendChild(backdrop);

                // Trigger backdrop fade in
                setTimeout(() => {
                    backdrop.style.opacity = '1';
                }, 10);

                const modal = document.createElement('div');
                modal.id = 'refreshTimeModal';
                modal.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(255, 255, 255, 0.95);
                    padding: 25px;
                    border-radius: 25px;
                    opacity: 0;
                    filter: blur(10px);
                    transition: all 0.5s ease;
                    width: 220px;
                    backdrop-filter: blur(20px);
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    z-index: 10001;
                    font-family: sans-serif;
                    text-align: center;
                `;

                modal.innerHTML = `
                    <label for="refreshTimeSelect" style="display: block; margin-bottom: 15px; font-size: 16px; color: #333; font-weight: 500;">Refresh every:</label>
                    <select id="refreshTimeSelect" style="margin: 0 8px; padding: 8px 12px; font-size: 14px; border-radius: 8px; border: 1px solid #ddd; background: white;">
                        <option value="5">5s</option>
                        <option value="6">6s</option>
                        <option value="7">7s</option>
                        <option value="8">8s</option>
                        <option value="9">9s</option>
                        <option value="10">10s</option>
                        <option value="15">15s</option>
                        <option value="20">20s</option>
                        <option value="30">30s</option>
                        <option value="60">60s</option>
                    </select>
                    <button id="okBtn" style="margin-left: 12px; padding: 8px 16px; font-size: 14px; background: #4CAF50; color: white; border: none; border-radius: 8px; cursor: pointer; transition: background 0.3s ease;">OK</button>
                `;

                document.body.appendChild(modal);
                console.log('[TrueBDC AutoRefresh] Modal created and added to DOM');

                // Trigger modal fade in
                setTimeout(() => {
                    modal.style.opacity = '1';
                    modal.style.filter = 'blur(0px)';
                }, 10);

                document.getElementById('refreshTimeSelect').value = refreshTime;
                document.getElementById('okBtn').onclick = function() {
                    console.log('[TrueBDC AutoRefresh] OK button clicked');
                    refreshTime = Number(document.getElementById('refreshTimeSelect').value);
                    localStorage.setItem('autoRefreshTime', refreshTime);
                    console.log('[TrueBDC AutoRefresh] New refresh time set:', refreshTime);
                    closeModal();
                    restart();
                };

                // OK button hover effect
                const okBtn = document.getElementById('okBtn');
                okBtn.addEventListener('mouseenter', () => {
                    okBtn.style.background = '#45a049';
                });
                okBtn.addEventListener('mouseleave', () => {
                    okBtn.style.background = '#4CAF50';
                });

                modal.addEventListener('click', e => e.stopPropagation());
                backdrop.addEventListener('click', closeModal);

                // Close on Escape key
                const escapeHandler = (e) => {
                    if (e.key === 'Escape') {
                        closeModal();
                        document.removeEventListener('keydown', escapeHandler);
                    }
                };
                document.addEventListener('keydown', escapeHandler);
            }

            /**
             * CLOSE MODAL - Close the modal with fade out animation.
             */
            function closeModal() {
                console.log('[TrueBDC AutoRefresh] closeModal called');
                const modal = document.getElementById('refreshTimeModal');
                const backdrop = document.getElementById('modalBackdrop');

                if (modal) {
                    modal.style.opacity = '0';
                    modal.style.filter = 'blur(10px)';
                    setTimeout(() => {
                        modal.remove();
                        console.log('[TrueBDC AutoRefresh] Modal removed');
                    }, 300);
                }

                if (backdrop) {
                    backdrop.style.opacity = '0';
                    setTimeout(() => {
                        backdrop.remove();
                        console.log('[TrueBDC AutoRefresh] Backdrop removed');
                    }, 300);
                }

                isPaused = false;
                updateDisplay();
            }

            /**
             * Update the timer display.
             */
            function updateDisplay() {
                createTimerDisplay();
            }

            /**
             * Start the auto-refresh timer.
             */
            function start() {
                console.log('[TrueBDC AutoRefresh] start() called');
                if (intervalId) {
                    clearInterval(intervalId);
                    console.log('[TrueBDC AutoRefresh] Cleared existing interval');
                }
                countdown = refreshTime;
                updateDisplay();
                intervalId = setInterval(() => {
                    if (!isPaused) {
                        countdown--;
                        updateDisplay();
                        console.log('[TrueBDC AutoRefresh] Countdown:', countdown);
                        if (countdown <= 0) {
                            console.log('[TrueBDC AutoRefresh] Countdown reached 0, refreshing page...');
                            document.getElementById('autoRefreshTimerDisplay').textContent = '🔄 Refreshing...';
                            setTimeout(() => window.location.reload(), 700);
                            clearInterval(intervalId);
                        }
                    }
                }, 1000);
                console.log('[TrueBDC AutoRefresh] Timer started with interval:', intervalId);
            }

            /**
             * Restart the timer.
             */
            function restart() {
                console.log('[TrueBDC AutoRefresh] restart() called');
                isPaused = false;
                start();
            }

            // Initialize
            console.log('[TrueBDC AutoRefresh] Initializing timer...');
            start();

            // Keyboard shortcut: Space to pause/resume
            document.addEventListener('keydown', e => {
                if (e.code === 'Space' && !document.getElementById('refreshTimeModal')) {
                    console.log('[TrueBDC AutoRefresh] Spacebar pressed, toggling pause');
                    isPaused = !isPaused;
                    updateDisplay();
                }
            });

            console.log('[TrueBDC AutoRefresh] Script initialization complete');

        })();
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        console.log('[TrueBDC AutoRefresh] Settings updated:', newSettings);
    }

    onPageChange() {
        console.log('[TrueBDC AutoRefresh] Page change detected');
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        console.log('[TrueBDC AutoRefresh] Destroying script...');
        try {
            // Remove UI elements
            const timer = document.getElementById('autoRefreshTimerDisplay');
            if (timer && timer.parentNode) {
                timer.parentNode.removeChild(timer);
                console.log('[TrueBDC AutoRefresh] Timer element removed');
            }

            const modal = document.getElementById('refreshTimeModal');
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
                console.log('[TrueBDC AutoRefresh] Modal element removed');
            }

            this.isActive = false;
            console.log('[TrueBDC AutoRefresh] Script destroyed');
        } catch (error) {
            console.error('[TrueBDC AutoRefresh] Error destroying script:', error);
        }
    }

    static isAvailable() {
        const url = window.location.href;
        return url.includes('eleadcrm.com') && url.includes('/elead_track/weblink/weblinkToday.aspx');
    }

    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasTimer: !!document.getElementById('autoRefreshTimerDisplay')
        };
    }
}

// Make class globally available
window.AutoRefresh = AutoRefresh;
