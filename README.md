# TrueBDC CRM Automation Suite

A comprehensive Chrome extension that consolidates automotive dealership CRM automation tools into a single, manageable platform. This extension targets eLeadCRM and VinSolutions systems used by automotive dealerships for lead management and sales operations.

## 🚀 Current Implementation Status

### ✅ Completed Features (Phase 1)

1. **Chrome Extension Foundation**
   - Manifest V3 structure with iframe support
   - Modern popup interface with tabs
   - Background service worker
   - Advanced content script injection system
   - Iframe detection and script injection capabilities
   - Storage management for settings and profiles

2. **Dynamic Tab Title Changer**
   - Changes browser tab titles to custom dealership names
   - Supports mutation observer for dynamic title changes
   - Configurable dealership names
   - Works on eLeadCRM and VinSolutions

3. **Bypass Refresh Confirmation**
   - Bypasses F5 and Ctrl+R refresh confirmation dialogs
   - Prevents beforeunload confirmation prompts
   - Shows refresh notifications
   - Cross-platform keyboard support

4. **TrueBDC Click to Call**
   - Automatically detects phone numbers on pages
   - Adds clickable phone icons next to numbers
   - Tracks dial counts and prevents excessive calling
   - Confirmation dialog for repeated calls
   - Supports multiple phone number formats

5. **Tab to Popup Converter**
   - Converts browser tabs to popup windows with Ctrl+Alt+9
   - Remembers window sizes for different page types
   - Advanced positioning and resize tooltips
   - Cross-platform keyboard shortcuts (Windows/Mac)

6. **Auto Refresh with Timer**
   - Beautiful progress bar timer display in corner
   - Elegant fade blur modal for settings
   - Customizable refresh intervals (5s to 60s)
   - Smooth animations and hover effects
   - Keyboard shortcuts (ESC to close, click outside to dismiss)

### 🔄 In Progress

7. **Add Calling Text and Time Shortcut** (Next)
8. **eLeads Faster Rooftops** (Complex - requires Airtable integration)

## 📁 Project Structure

```
TrueBDCScr/
├── manifest.json              # Extension manifest (Manifest V3)
├── popup.html                 # Extension popup interface
├── popup.js                   # Popup functionality
├── background.js              # Service worker
├── content.js                 # Main content script coordinator
├── styles/
│   ├── popup.css              # Popup interface styles
│   └── content.css            # Content script styles
├── scripts/
│   ├── utils.js               # Utility functions
│   ├── dynamic-tab-title.js   # Tab title changer
│   ├── bypass-refresh.js      # Refresh confirmation bypass
│   ├── click-to-call.js       # Phone number click-to-call
│   ├── tab-to-popup.js        # Tab to popup converter
│   ├── auto-refresh.js        # (To be implemented)
│   ├── calling-text.js        # (To be implemented)
│   └── faster-rooftops.js     # (To be implemented)
├── icons/
│   └── README.md              # Icon requirements
└── README.md                  # This file
```

## 🛠️ Installation & Setup

### Prerequisites
- Google Chrome browser
- Access to eLeadCRM or VinSolutions CRM systems

### Installation Steps

1. **Download/Clone the Extension**
   ```bash
   git clone <repository-url>
   cd TrueBDCScr
   ```

2. **Create Extension Icons**
   - Create PNG icons in the `icons/` directory:
     - `icon16.png` (16x16)
     - `icon32.png` (32x32) 
     - `icon48.png` (48x48)
     - `icon128.png` (128x128)
   - Use blue theme with "BDC" or "TrueBDC" text

3. **Load Extension in Chrome**
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" (top right toggle)
   - Click "Load unpacked"
   - Select the `TrueBDCScr` folder
   - The extension should now appear in your extensions list

4. **Configure Settings**
   - Click the TrueBDC extension icon in Chrome toolbar
   - Go to "Settings" tab
   - Configure:
     - Dealership Name (for tab titles)
     - Agent Name
     - Airtable API credentials (for future features)

## 🎯 Usage Instructions

### Dynamic Tab Title Changer
- **Auto-activates** on supported CRM pages
- Changes tab title to your configured dealership name
- No manual action required

### Bypass Refresh Confirmation
- **Auto-activates** on supported CRM pages
- Press **F5** or **Ctrl+R** to refresh without confirmation
- Shows brief "Refreshing..." notification

### Click to Call
- **Auto-activates** on CRM pages with phone numbers
- Look for 📞 icons next to phone numbers
- Click icon to initiate call
- Tracks call counts and shows confirmations for repeated calls

### Tab to Popup Converter
- **Keyboard shortcut**: **Ctrl+Alt+9** (Windows) or **Cmd+Option+9** (Mac)
- **Target Page**: Works on `/elead_track/weblink/weblinkToday.aspx` (same as auto-refresh timer)
- **True Popup Windows**: Uses Chrome Windows API for complete address bar removal
- **Enhanced Memory**: Remembers both window size AND position preferences
- **Custom Titles**: Click blue title banner to customize popup window titles
- **Smart Auto-Refresh**: Only dims timer display during window manipulation (keeps refresh running)
- **Persistent Storage**: All configurations saved across browser sessions via Chrome storage
- **Auto-Save**: Saves position/size changes and custom titles automatically
- **Visual Feedback**: Separate confirmations for position/size and title saves
- **Conflict-Free Operation**: No interference with auto-refresh functionality
- **Professional Interface**: Clean modal for title editing with keyboard support
- **Iframe Support**: Converts entire tab when triggered from supported iframe

### Auto Refresh with Timer
- **Auto-activates** on supported CRM pages (weblinkToday.aspx)
- **Compact Timer Display**: Minimal progress bar timer in bottom-left corner (max 200px width)
- **Click Timer**: Opens elegant modal positioned near timer
- **Available Intervals**: 5s, 6s, 7s, 8s, 9s, 10s, 15s, 20s, 30s, 60s
- **Keyboard Controls**:
  - **ESC**: Close settings modal
  - **Click outside**: Dismiss modal
- **Visual Features**:
  - Compact, content-fitted design with minimal padding
  - Animated progress bar showing countdown
  - Smooth hover effects with elevation
  - Glassmorphism modal with blur effects
  - Positioned near timer (no full-screen overlay)
  - Pause indicator when modal is open

## ⚙️ Configuration Options

### General Settings
- **Dealership Name**: Used for tab titles
- **Agent Name**: Used in various scripts
- **Enable Notifications**: Toggle system notifications

### Script Controls
Each script can be individually enabled/disabled via toggle switches in the popup interface.

### Profile Management
- Create multiple dealership profiles
- Switch between different configurations
- Export/import settings as JSON

## 🔧 Development

### Iframe Injection Architecture

The TrueBDC extension now includes advanced iframe injection capabilities that work like Tampermonkey/Greasemonkey:

#### **Key Features:**
- **Automatic Iframe Detection**: Scripts automatically inject into both main pages and iframes
- **Frame Context Awareness**: Each script knows whether it's running in main frame or iframe
- **Cross-Frame Communication**: Main frame and iframes can communicate for coordinated functionality
- **Selective Script Execution**: Some scripts run only in main frame, others in all frames

#### **Technical Implementation:**
- `"all_frames": true` in manifest.json enables iframe injection
- `"match_about_blank": true` handles dynamically created iframes
- Frame context detection using `window !== window.top`
- Message passing between frames for coordination
- Background script monitoring of frame contexts

#### **Script Categories:**
- **Main Frame Only**: `dynamicTabTitle`, `tabToPopup` (affect browser-level features)
- **All Frames**: `clickToCall`, `bypassRefresh`, `callingText`, `autoRefresh` (work within content)

### Adding New Scripts
1. Create new script file in `scripts/` directory
2. Follow the class-based pattern used by existing scripts
3. Add script to `manifest.json` content_scripts
4. Update `content.js` to initialize the new script
5. Add toggle switch to `popup.html`
6. Consider iframe compatibility in script design

### Script Structure Template
```javascript
class NewScript {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.init();
    }

    init() {
        // Initialization logic
    }

    isSupportedPage() {
        // Check if script should run on current page
    }

    updateSettings(newSettings) {
        // Handle settings updates
    }

    onPageChange() {
        // Handle SPA page changes
    }

    destroy() {
        // Cleanup when script is disabled
    }

    static isAvailable() {
        // Check if script is available for current site
    }

    getStatus() {
        // Return current script status
    }
}

window.NewScript = NewScript;
```

## 🎨 UI Features

### Modern Popup Interface
- **Tabbed navigation**: Scripts, Settings, Profiles
- **Toggle switches**: Individual script control
- **Professional styling**: Automotive dealership theme
- **Responsive design**: Works on different screen sizes

### Visual Feedback
- **Status messages**: Success/error notifications
- **Activity logging**: Background activity tracking
- **Tooltips**: Helpful hints and information
- **Progress indicators**: Loading states and animations

## 🔒 Security & Privacy

- **Local storage only**: No data sent to external servers (except Airtable integration)
- **Minimal permissions**: Only requests necessary Chrome permissions
- **Secure API handling**: Encrypted storage for sensitive data
- **Content script isolation**: Scripts run in isolated environment

## 🐛 Troubleshooting

### Common Issues

1. **Extension not loading**
   - Check that all required files are present
   - Verify manifest.json syntax
   - Check Chrome developer console for errors

2. **Scripts not activating**
   - Verify you're on a supported CRM page
   - Check that scripts are enabled in popup
   - Look for JavaScript errors in console

3. **Click to call not working**
   - Ensure phone numbers are in supported formats
   - Check that browser allows tel: links
   - Verify popup blockers aren't interfering

4. **Tab to popup not working**
   - Check popup blocker settings
   - Verify keyboard shortcut (Ctrl+Alt+9)
   - Ensure page supports window.open()

### Debug Mode
Enable debug mode in settings to see detailed logging in browser console.

### Iframe-Specific Issues

1. **Scripts not working in eLeadCRM iframes**
   - Check browser console for iframe injection logs
   - Verify the iframe URL matches supported patterns
   - Ensure `all_frames: true` is set in manifest.json

2. **Frame context detection issues**
   - Look for frame context reports in background script logs
   - Check if cross-origin restrictions are blocking detection
   - Verify iframe communication is working

3. **Script conflicts between frames**
   - Some scripts are designed to run only in main frame
   - Check iframe manager logs for script filtering
   - Verify frame-specific settings are applied correctly

### Debugging Iframe Injection
```javascript
// Check frame context in console
console.log('Frame context:', TrueBDCUtils.getFrameContext());

// Check if script should run in current frame
console.log('Should run script:', TrueBDCUtils.isIframeSupported('scriptName'));

// Monitor iframe detection
// Background script logs will show iframe_detected events
```

## 📋 Roadmap

### Phase 2 (Remaining Priority Scripts)
- [ ] Auto Refresh with Configurable Timer
- [ ] Add Calling Text and Time Shortcut
- [ ] eLeads Faster Rooftops (with Airtable integration)

### Phase 3 (Additional Scripts)
- [ ] Auto Close Release Notes
- [ ] Message Box with Dealership Info
- [ ] Find Internet Up Activity
- [ ] Email Notifications
- [ ] And 10 more scripts from original collection

### Phase 4 (Enhancements)
- [ ] Advanced profile management
- [ ] Bulk configuration import/export
- [ ] Analytics and reporting
- [ ] Multi-dealership support
- [ ] Cloud sync capabilities

## 📞 Support

For technical support or feature requests, contact your system administrator or development team.

## 📄 License

This extension is proprietary software developed for automotive dealership use.
