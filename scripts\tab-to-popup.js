// TrueBDC CRM Automation Suite - Tab to Popup Converter

class TabToPopup {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.popup = null;

        // Enhanced default configuration with position
        this.defaultConfig = {
            width: 1200,
            height: 800,
            left: Math.round((screen.width - 1200) / 2),
            top: Math.round((screen.height - 800) / 2)
        };

        // Enhanced storage for both size and position
        this.savedConfigs = new Map();
        this.currentUrlKey = null;
        this.saveTimeout = null;

        this.init();
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            TrueBDCUtils.log('Initializing Tab to Popup Converter', {
                frameContext: frameContext
            });

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.loadSavedConfigs();
                this.setupKeyListener();
                this.isActive = true;

                TrueBDCUtils.log('Tab to Popup Converter activated', {
                    frameContext: frameContext
                });
                TrueBDCUtils.logActivity('tab_to_popup_activated', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('Tab to Popup Converter not activated - unsupported page', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Tab to Popup Converter', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const frameContext = TrueBDCUtils.getFrameContext();

        // Use EXACT same URL matching as auto-refresh timer
        // Only support the same iframe where auto-refresh timer works
        const isSupported = url.includes('eleadcrm.com') &&
                           url.includes('/elead_track/weblink/weblinkToday.aspx');

        TrueBDCUtils.log('Tab to Popup page support check', {
            url: url,
            frameContext: frameContext,
            isSupported: isSupported,
            lookingFor: '/elead_track/weblink/weblinkToday.aspx'
        });

        return isSupported;
    }

    async loadSavedConfigs() {
        try {
            const result = await chrome.storage.local.get('popupConfigs');
            if (result.popupConfigs) {
                this.savedConfigs = new Map(Object.entries(result.popupConfigs));
                TrueBDCUtils.log('Loaded saved popup configurations', Object.fromEntries(this.savedConfigs));
            } else {
                // Migrate old popupSizes data if it exists
                const oldResult = await chrome.storage.local.get('popupSizes');
                if (oldResult.popupSizes) {
                    const oldSizes = Object.entries(oldResult.popupSizes);
                    for (const [key, size] of oldSizes) {
                        this.savedConfigs.set(key, {
                            width: size.width,
                            height: size.height,
                            left: Math.round((screen.width - size.width) / 2),
                            top: Math.round((screen.height - size.height) / 2)
                        });
                    }
                    await this.saveConfigs();
                    TrueBDCUtils.log('Migrated old popup sizes to new configuration format');
                }
            }
        } catch (error) {
            TrueBDCUtils.error('Error loading saved popup configurations', error);
        }
    }

    async saveConfigs() {
        try {
            const configsObject = Object.fromEntries(this.savedConfigs);
            await chrome.storage.local.set({ popupConfigs: configsObject });
            TrueBDCUtils.log('Saved popup configurations', configsObject);

            // Show visual feedback
            this.showConfigSavedNotification();
        } catch (error) {
            TrueBDCUtils.error('Error saving popup configurations', error);
        }
    }

    showConfigSavedNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-config-saved-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 3px 10px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>✓</span>
                <span>Popup position & size saved</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Check for Ctrl+Alt+9 (Windows) or Cmd+Option+9 (Mac)
            const isWindows = (event.ctrlKey && event.altKey && event.code === 'Digit9');
            const isMac = (event.metaKey && event.altKey && event.code === 'Digit9');
            
            if (isWindows || isMac) {
                TrueBDCUtils.log('Tab to popup shortcut detected', {
                    platform: isWindows ? 'Windows' : 'Mac',
                    ctrlKey: event.ctrlKey,
                    altKey: event.altKey,
                    metaKey: event.metaKey,
                    code: event.code
                });

                event.preventDefault();
                event.stopPropagation();

                this.convertTabToPopup();

                TrueBDCUtils.logActivity('tab_to_popup_triggered', {
                    platform: isWindows ? 'Windows' : 'Mac',
                    timestamp: new Date().toISOString()
                });

                return false;
            }
        };

        // Add event listener
        window.addEventListener('keydown', this.keydownHandler, true);
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    async convertTabToPopup() {
        try {
            // Show conversion notification
            this.showConversionNotification();

            // Detect iframe context
            const frameContext = TrueBDCUtils.getFrameContext();

            // Get current page info - use top window URL if in iframe for tab conversion
            const currentUrl = frameContext.isIframe ? window.top.location.href : window.location.href;
            const currentTitle = frameContext.isIframe ?
                (window.top.document.title || document.title) : document.title;
            const urlKey = this.getUrlKey(currentUrl);

            TrueBDCUtils.log('Converting to popup', {
                frameContext: frameContext,
                currentUrl: currentUrl,
                currentTitle: currentTitle
            });

            // Get saved configuration for this URL or use default
            const savedConfig = this.savedConfigs.get(urlKey) || this.defaultConfig;
            this.currentUrlKey = urlKey;

            TrueBDCUtils.log('Using popup configuration', {
                urlKey: urlKey,
                config: savedConfig
            });

            // Enhanced popup features - cleaner interface without address bar
            const features = [
                `width=${savedConfig.width}`,
                `height=${savedConfig.height}`,
                `left=${savedConfig.left}`,
                `top=${savedConfig.top}`,
                'menubar=no',
                'toolbar=no',
                'location=no',  // Remove address bar for cleaner interface
                'status=yes',
                'scrollbars=yes',
                'resizable=yes'
            ].join(',');

            TrueBDCUtils.log('Opening popup window', {
                url: currentUrl,
                config: savedConfig,
                features: features
            });

            // Open popup window
            this.popup = window.open(currentUrl, '_blank', features);

            if (this.popup) {
                // Set up popup event handlers
                this.setupPopupHandlers(this.popup, urlKey);

                // Show success message
                setTimeout(() => {
                    this.showResizeTooltip();
                }, 1000);

                // Close current tab after a short delay
                // If in iframe, close the top window; otherwise close current window
                setTimeout(() => {
                    if (frameContext.isIframe) {
                        try {
                            window.top.close();
                        } catch (error) {
                            TrueBDCUtils.log('Cannot close top window from iframe, trying current window');
                            window.close();
                        }
                    } else {
                        window.close();
                    }
                }, 500);

            } else {
                throw new Error('Failed to open popup window - popup blocked?');
            }

        } catch (error) {
            TrueBDCUtils.error('Error converting tab to popup', error);
            this.showErrorNotification('Failed to convert tab to popup. Please check popup blocker settings.');
        }
    }

    setupPopupHandlers(popup, urlKey) {
        // Enhanced handler to track both position and size changes
        let changeTimeout;

        const handleConfigChange = () => {
            clearTimeout(changeTimeout);
            clearTimeout(this.saveTimeout);

            // Debounce to avoid excessive saves during dragging/resizing
            changeTimeout = setTimeout(() => {
                if (popup && !popup.closed) {
                    const newConfig = {
                        width: popup.outerWidth,
                        height: popup.outerHeight,
                        left: popup.screenX,
                        top: popup.screenY
                    };

                    // Only save if configuration actually changed
                    const currentConfig = this.savedConfigs.get(urlKey) || this.defaultConfig;
                    const hasChanged =
                        newConfig.width !== currentConfig.width ||
                        newConfig.height !== currentConfig.height ||
                        newConfig.left !== currentConfig.left ||
                        newConfig.top !== currentConfig.top;

                    if (hasChanged) {
                        this.savedConfigs.set(urlKey, newConfig);

                        // Debounce the actual save to storage
                        this.saveTimeout = setTimeout(() => {
                            this.saveConfigs();
                        }, 500);

                        TrueBDCUtils.log('Popup configuration changed', {
                            urlKey: urlKey,
                            config: newConfig,
                            changed: {
                                size: newConfig.width !== currentConfig.width || newConfig.height !== currentConfig.height,
                                position: newConfig.left !== currentConfig.left || newConfig.top !== currentConfig.top
                            }
                        });
                    }
                }
            }, 300); // Short delay to capture final position/size
        };

        // Set up event listeners for both resize and move
        popup.addEventListener('resize', handleConfigChange);

        // Monitor position changes by polling (since there's no move event)
        let lastPosition = { left: popup.screenX, top: popup.screenY };
        const positionMonitor = setInterval(() => {
            if (popup.closed) {
                clearInterval(positionMonitor);
                return;
            }

            const currentPosition = { left: popup.screenX, top: popup.screenY };
            if (currentPosition.left !== lastPosition.left || currentPosition.top !== lastPosition.top) {
                lastPosition = currentPosition;
                handleConfigChange();
            }
        }, 500);

        // Handle popup close
        popup.addEventListener('beforeunload', () => {
            clearInterval(positionMonitor);
            clearTimeout(changeTimeout);
            clearTimeout(this.saveTimeout);

            TrueBDCUtils.log('Popup closing');
            TrueBDCUtils.logActivity('popup_closed', {
                urlKey: urlKey,
                finalConfig: this.savedConfigs.get(urlKey)
            });
        });
    }

    getUrlKey(url) {
        // Create a key for storing popup configurations (size + position) based on URL pattern
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            
            // Extract meaningful part of the path
            if (pathname.includes('weblinkToday.aspx')) {
                return 'weblink';
            } else if (pathname.includes('reports')) {
                return 'reports';
            } else if (pathname.includes('NewProspects')) {
                return 'prospects';
            } else if (pathname.includes('elead_mail')) {
                return 'mail';
            } else {
                return 'default';
            }
        } catch (error) {
            return 'default';
        }
    }

    showConversionNotification() {
        const frameContext = TrueBDCUtils.getFrameContext();

        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-popup-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #007bff, #0056b3)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        const message = frameContext.isIframe ?
            'Converting iframe content to popup window...' :
            'Converting tab to popup window...';

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="truebdc-spinner" style="width: 16px; height: 16px; border-width: 2px;"></div>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after conversion
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    showResizeTooltip() {
        const tooltip = TrueBDCUtils.createElement('div', {
            id: 'truebdc-resize-tooltip'
        }, {
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            fontSize: '13px',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '300px'
        });

        tooltip.innerHTML = `
            <div>
                <strong>💡 Tip:</strong> Resize this window to your preferred size. 
                The size will be remembered for next time!
            </div>
        `;

        document.body.appendChild(tooltip);

        // Fade in
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 100);

        // Fade out after 5 seconds
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 5000);
    }

    showErrorNotification(message) {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-error-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #dc3545, #c82333)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(220, 53, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '350px'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span>⚠️</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('Tab to Popup settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                window.removeEventListener('keydown', this.keydownHandler, true);
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            // Close popup if open
            if (this.popup && !this.popup.closed) {
                this.popup.close();
                this.popup = null;
            }

            this.isActive = false;
            
            TrueBDCUtils.log('Tab to Popup Converter destroyed');
            TrueBDCUtils.logActivity('tab_to_popup_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Tab to Popup Converter', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            savedSizesCount: this.savedSizes.size,
            popupOpen: this.popup && !this.popup.closed
        };
    }
}

// Make class globally available
window.TabToPopup = TabToPopup;
