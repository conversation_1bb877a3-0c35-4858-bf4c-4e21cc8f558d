var seiri = {
  map: new Map(),
  execute: function (tab) {
    chrome.windows.get(tab.windowId, function (window) {
      if (window.type === "popup") {
        seiri.win2Tab(tab, window);
      } else {
        seiri.tab2Win(tab, window);
      }
    });
  },
  tab2Win: function (tab, window) {
    var ps = window || false;
    var data = { type: "popup" };
    if (tab) {
      data.tabId = tab.id;
      seiri.map.set(tab.id, window.id);
    }
    if (ps && window.state === "normal") {
      data.top = window.top;
      data.left = window.left;
      data.width = window.width;
      data.height = window.height;
    }
    chrome.windows.create(data, null);
  },
  win2Tab: function (tab, wp) {
    var windowId = seiri.map.get(tab.id);
    if (windowId) {
      chrome.windows.get(windowId, function (w) {
        if (!chrome.runtime.lastError) {
          seiri.tnwt(tab, windowId);
        } else {
          seiri.tnwlf(tab, wp);
        }
      });
    } else {
      seiri.tnwlf(tab, wp);
    }
  },
  tnwlf: function (tab, wp) {
    chrome.windows.getLastFocused(
      { windowTypes: ["normal"] },
      function (window) {
        if (!chrome.runtime.lastError) {
          seiri.tnwt(tab, window.id);
        } else {
          seiri.tnwc(tab, wp);
        }
      }
    );
  },
  tnwc: function (tab, wp) {
    chrome.windows.create({ type: "normal", state: wp.state, tabId: tab.id });
  },
  tnwt: function (tab, windowId) {
    chrome.tabs.query({ windowId: windowId, active: true }, function (ts) {
      chrome.tabs.move(
        tab.id,
        { windowId: windowId, index: ts[0].index + 1 },
        function (t) {
          if (tab) chrome.tabs.update(tab.id, { active: true });
          chrome.windows.update(windowId, { focused: true });
        }
      );
      seiri.map.delete(tab.id);
    });
  },
};
chrome.tabs.onRemoved.addListener(function (tab, i) {
  seiri.map.delete(tab);
});
chrome.action.onClicked.addListener(function (tab) {
  chrome.windows.get(tab.windowId, function (window) {
    seiri.tab2Win(tab, window);
  });
});
chrome.contextMenus.removeAll();
chrome.contextMenus.create({
  id: "toggle_window",
  title: "Toggle Open As Window",
  type: "normal",
  contexts: ["all"],
});
chrome.contextMenus.onClicked.addListener((t) => {
  chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
    seiri.execute(tabs[0]);
  });
});
