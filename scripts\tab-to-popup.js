// TrueBDC CRM Automation Suite - Tab to Popup Converter

class TabToPopup {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.popup = null;

        // Enhanced default configuration with position
        this.defaultConfig = {
            width: 1200,
            height: 800,
            left: Math.round((screen.width - 1200) / 2),
            top: Math.round((screen.height - 800) / 2)
        };

        // Enhanced storage for both size and position
        this.savedConfigs = new Map();
        this.savedTitles = new Map();
        this.currentUrlKey = null;
        this.saveTimeout = null;
        this.isResizing = false;
        this.resizeEndTimeout = null;

        this.init();
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            TrueBDCUtils.log('Initializing Tab to Popup Converter', {
                frameContext: frameContext
            });

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.loadSavedConfigs();
                this.setupKeyListener();
                this.isActive = true;

                TrueBDCUtils.log('Tab to Popup Converter activated', {
                    frameContext: frameContext
                });
                TrueBDCUtils.logActivity('tab_to_popup_activated', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('Tab to Popup Converter not activated - unsupported page', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Tab to Popup Converter', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const frameContext = TrueBDCUtils.getFrameContext();

        // Use EXACT same URL matching as auto-refresh timer
        // Only support the same iframe where auto-refresh timer works
        const isSupported = url.includes('eleadcrm.com') &&
                           url.includes('/elead_track/weblink/weblinkToday.aspx');

        TrueBDCUtils.log('Tab to Popup page support check', {
            url: url,
            frameContext: frameContext,
            isSupported: isSupported,
            lookingFor: '/elead_track/weblink/weblinkToday.aspx'
        });

        return isSupported;
    }

    async loadSavedConfigs() {
        try {
            // Load popup configurations
            const result = await chrome.storage.local.get(['popupConfigs', 'popupTitles']);

            if (result.popupConfigs) {
                this.savedConfigs = new Map(Object.entries(result.popupConfigs));
                TrueBDCUtils.log('Loaded saved popup configurations', Object.fromEntries(this.savedConfigs));
            } else {
                // Migrate old popupSizes data if it exists
                const oldResult = await chrome.storage.local.get('popupSizes');
                if (oldResult.popupSizes) {
                    const oldSizes = Object.entries(oldResult.popupSizes);
                    for (const [key, size] of oldSizes) {
                        this.savedConfigs.set(key, {
                            width: size.width,
                            height: size.height,
                            left: Math.round((screen.width - size.width) / 2),
                            top: Math.round((screen.height - size.height) / 2)
                        });
                    }
                    await this.saveConfigs();
                    TrueBDCUtils.log('Migrated old popup sizes to new configuration format');
                }
            }

            // Load custom titles
            if (result.popupTitles) {
                this.savedTitles = new Map(Object.entries(result.popupTitles));
                TrueBDCUtils.log('Loaded saved popup titles', Object.fromEntries(this.savedTitles));
            }
        } catch (error) {
            TrueBDCUtils.error('Error loading saved popup configurations', error);
        }
    }

    async saveConfigs() {
        try {
            const configsObject = Object.fromEntries(this.savedConfigs);
            await chrome.storage.local.set({ popupConfigs: configsObject });
            TrueBDCUtils.log('Saved popup configurations', configsObject);

            // Show visual feedback
            this.showConfigSavedNotification();
        } catch (error) {
            TrueBDCUtils.error('Error saving popup configurations', error);
        }
    }

    async saveTitles() {
        try {
            const titlesObject = Object.fromEntries(this.savedTitles);
            await chrome.storage.local.set({ popupTitles: titlesObject });
            TrueBDCUtils.log('Saved popup titles', titlesObject);

            // Show visual feedback for title save
            this.showTitleSavedNotification();
        } catch (error) {
            TrueBDCUtils.error('Error saving popup titles', error);
        }
    }

    showConfigSavedNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-config-saved-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 3px 10px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>✓</span>
                <span>Popup position & size saved</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    showTitleSavedNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-title-saved-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #17a2b8, #138496)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 3px 10px rgba(23, 162, 184, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>✓</span>
                <span>Popup title saved</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Check for Ctrl+Alt+9 (Windows) or Cmd+Option+9 (Mac)
            const isWindows = (event.ctrlKey && event.altKey && event.code === 'Digit9');
            const isMac = (event.metaKey && event.altKey && event.code === 'Digit9');
            
            if (isWindows || isMac) {
                TrueBDCUtils.log('Tab to popup shortcut detected', {
                    platform: isWindows ? 'Windows' : 'Mac',
                    ctrlKey: event.ctrlKey,
                    altKey: event.altKey,
                    metaKey: event.metaKey,
                    code: event.code
                });

                event.preventDefault();
                event.stopPropagation();

                this.convertTabToPopup();

                TrueBDCUtils.logActivity('tab_to_popup_triggered', {
                    platform: isWindows ? 'Windows' : 'Mac',
                    timestamp: new Date().toISOString()
                });

                return false;
            }
        };

        // Add event listener
        window.addEventListener('keydown', this.keydownHandler, true);
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    async convertTabToPopup() {
        try {
            // Show conversion notification
            this.showConversionNotification();

            // Detect iframe context
            const frameContext = TrueBDCUtils.getFrameContext();

            // Get current page info - use top window URL if in iframe for tab conversion
            const currentUrl = frameContext.isIframe ? window.top.location.href : window.location.href;
            const currentTitle = frameContext.isIframe ?
                (window.top.document.title || document.title) : document.title;
            const urlKey = this.getUrlKey(currentUrl);

            TrueBDCUtils.log('Converting to popup', {
                frameContext: frameContext,
                currentUrl: currentUrl,
                currentTitle: currentTitle
            });

            // Get saved configuration for this URL or use default
            const savedConfig = this.savedConfigs.get(urlKey) || this.defaultConfig;
            this.currentUrlKey = urlKey;

            TrueBDCUtils.log('Using popup configuration', {
                urlKey: urlKey,
                config: savedConfig
            });

            // Enhanced popup features - cleaner interface without address bar
            // Use more aggressive settings to hide browser chrome
            const features = [
                `width=${savedConfig.width}`,
                `height=${savedConfig.height}`,
                `left=${savedConfig.left}`,
                `top=${savedConfig.top}`,
                'menubar=no',
                'toolbar=no',
                'location=no',
                'status=no',      // Hide status bar
                'titlebar=no',    // Hide title bar if supported
                'directories=no', // Hide directories
                'copyhistory=no', // Don't copy history
                'scrollbars=yes',
                'resizable=yes'
            ].join(',');

            TrueBDCUtils.log('Opening popup window', {
                url: currentUrl,
                config: savedConfig,
                features: features
            });

            // Open popup window
            this.popup = window.open(currentUrl, '_blank', features);

            if (this.popup) {
                // Set custom title if saved
                const savedTitle = this.savedTitles.get(urlKey);
                if (savedTitle) {
                    // Wait for popup to load before setting title
                    this.popup.addEventListener('load', () => {
                        this.popup.document.title = savedTitle;
                    });
                }

                // Set up popup event handlers
                this.setupPopupHandlers(this.popup, urlKey);

                // Set up title editing functionality
                this.setupTitleEditing(this.popup, urlKey);

                // Show success message
                setTimeout(() => {
                    this.showResizeTooltip();
                }, 1000);

                // Close current tab after a short delay
                // If in iframe, close the top window; otherwise close current window
                setTimeout(() => {
                    if (frameContext.isIframe) {
                        try {
                            window.top.close();
                        } catch (error) {
                            TrueBDCUtils.log('Cannot close top window from iframe, trying current window');
                            window.close();
                        }
                    } else {
                        window.close();
                    }
                }, 500);

            } else {
                throw new Error('Failed to open popup window - popup blocked?');
            }

        } catch (error) {
            TrueBDCUtils.error('Error converting tab to popup', error);
            this.showErrorNotification('Failed to convert tab to popup. Please check popup blocker settings.');
        }
    }

    setupPopupHandlers(popup, urlKey) {
        // Enhanced handler to track both position and size changes
        let changeTimeout;
        let autoRefreshPaused = false;

        const pauseAutoRefresh = () => {
            // Pause auto-refresh timer to prevent interference with dimension saving
            if (popup && popup.window && popup.window.isPaused !== undefined) {
                if (!popup.window.isPaused) {
                    popup.window.isPaused = true;
                    autoRefreshPaused = true;
                    TrueBDCUtils.log('Auto-refresh paused during resize');
                }
            }
        };

        const resumeAutoRefresh = () => {
            // Resume auto-refresh timer after resize is complete
            if (autoRefreshPaused && popup && popup.window && popup.window.isPaused !== undefined) {
                popup.window.isPaused = false;
                autoRefreshPaused = false;
                TrueBDCUtils.log('Auto-refresh resumed after resize');
            }
        };

        const handleConfigChange = (isResizeEvent = false) => {
            clearTimeout(changeTimeout);
            clearTimeout(this.saveTimeout);
            clearTimeout(this.resizeEndTimeout);

            if (isResizeEvent) {
                this.isResizing = true;
                pauseAutoRefresh();
            }

            // Debounce to avoid excessive saves during dragging/resizing
            changeTimeout = setTimeout(() => {
                if (popup && !popup.closed) {
                    const newConfig = {
                        width: popup.outerWidth,
                        height: popup.outerHeight,
                        left: popup.screenX,
                        top: popup.screenY
                    };

                    // Only save if configuration actually changed
                    const currentConfig = this.savedConfigs.get(urlKey) || this.defaultConfig;
                    const hasChanged =
                        newConfig.width !== currentConfig.width ||
                        newConfig.height !== currentConfig.height ||
                        newConfig.left !== currentConfig.left ||
                        newConfig.top !== currentConfig.top;

                    if (hasChanged) {
                        this.savedConfigs.set(urlKey, newConfig);

                        // Debounce the actual save to storage
                        this.saveTimeout = setTimeout(() => {
                            this.saveConfigs();
                        }, 500);

                        TrueBDCUtils.log('Popup configuration changed', {
                            urlKey: urlKey,
                            config: newConfig,
                            changed: {
                                size: newConfig.width !== currentConfig.width || newConfig.height !== currentConfig.height,
                                position: newConfig.left !== currentConfig.left || newConfig.top !== currentConfig.top
                            }
                        });
                    }
                }

                // Set up resize end detection to resume auto-refresh
                if (isResizeEvent) {
                    this.resizeEndTimeout = setTimeout(() => {
                        this.isResizing = false;
                        resumeAutoRefresh();
                    }, 1000); // Resume auto-refresh 1 second after resize stops
                }
            }, 300); // Short delay to capture final position/size
        };

        // Set up event listeners for both resize and move
        popup.addEventListener('resize', () => handleConfigChange(true));

        // Monitor position changes by polling (since there's no move event)
        let lastPosition = { left: popup.screenX, top: popup.screenY };
        const positionMonitor = setInterval(() => {
            if (popup.closed) {
                clearInterval(positionMonitor);
                return;
            }

            const currentPosition = { left: popup.screenX, top: popup.screenY };
            if (currentPosition.left !== lastPosition.left || currentPosition.top !== lastPosition.top) {
                lastPosition = currentPosition;
                handleConfigChange(false); // Position change, not resize
            }
        }, 500);

        // Handle popup close
        popup.addEventListener('beforeunload', () => {
            clearInterval(positionMonitor);
            clearTimeout(changeTimeout);
            clearTimeout(this.saveTimeout);

            TrueBDCUtils.log('Popup closing');
            TrueBDCUtils.logActivity('popup_closed', {
                urlKey: urlKey,
                finalConfig: this.savedConfigs.get(urlKey)
            });
        });
    }

    setupTitleEditing(popup, urlKey) {
        // Wait for popup to fully load before setting up title editing
        popup.addEventListener('load', () => {
            const popupDoc = popup.document;

            // Make title clickable for editing
            const titleElement = popupDoc.querySelector('title');
            if (!titleElement) return;

            // Create a visual indicator that title is clickable
            const titleClickArea = popupDoc.createElement('div');
            titleClickArea.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                height: 30px;
                background: rgba(0, 123, 255, 0.1);
                cursor: pointer;
                z-index: 999999;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: #007bff;
                border-bottom: 1px solid rgba(0, 123, 255, 0.2);
                transition: background 0.3s ease;
            `;
            titleClickArea.innerHTML = '📝 Click to edit title';
            titleClickArea.title = 'Click to customize popup window title';

            // Add hover effect
            titleClickArea.addEventListener('mouseenter', () => {
                titleClickArea.style.background = 'rgba(0, 123, 255, 0.2)';
            });
            titleClickArea.addEventListener('mouseleave', () => {
                titleClickArea.style.background = 'rgba(0, 123, 255, 0.1)';
            });

            // Add click handler for title editing
            titleClickArea.addEventListener('click', () => {
                this.showTitleEditModal(popup, urlKey);
            });

            popupDoc.body.appendChild(titleClickArea);
        });
    }

    showTitleEditModal(popup, urlKey) {
        const popupDoc = popup.document;
        const currentTitle = this.savedTitles.get(urlKey) || popupDoc.title;

        // Create modal overlay
        const overlay = popupDoc.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // Create modal content
        const modal = popupDoc.createElement('div');
        modal.style.cssText = `
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            width: 300px;
            font-family: sans-serif;
        `;

        modal.innerHTML = `
            <h3 style="margin: 0 0 15px 0; color: #333;">Customize Popup Title</h3>
            <input type="text" id="titleInput" value="${currentTitle}" style="
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                margin-bottom: 15px;
                box-sizing: border-box;
            ">
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button id="cancelBtn" style="
                    padding: 8px 16px;
                    border: 1px solid #ddd;
                    background: white;
                    border-radius: 4px;
                    cursor: pointer;
                ">Cancel</button>
                <button id="saveBtn" style="
                    padding: 8px 16px;
                    border: none;
                    background: #007bff;
                    color: white;
                    border-radius: 4px;
                    cursor: pointer;
                ">Save</button>
            </div>
        `;

        overlay.appendChild(modal);
        popupDoc.body.appendChild(overlay);

        // Focus and select input
        const input = modal.querySelector('#titleInput');
        input.focus();
        input.select();

        // Handle save
        const saveBtn = modal.querySelector('#saveBtn');
        const cancelBtn = modal.querySelector('#cancelBtn');

        const saveTitle = () => {
            const newTitle = input.value.trim();
            if (newTitle && newTitle !== currentTitle) {
                // Save to storage
                this.savedTitles.set(urlKey, newTitle);
                this.saveTitles();

                // Update popup title immediately
                popupDoc.title = newTitle;

                TrueBDCUtils.log('Popup title updated', {
                    urlKey: urlKey,
                    newTitle: newTitle
                });
            }
            overlay.remove();
        };

        const cancelEdit = () => {
            overlay.remove();
        };

        saveBtn.addEventListener('click', saveTitle);
        cancelBtn.addEventListener('click', cancelEdit);

        // Handle Enter and Escape keys
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveTitle();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });

        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                cancelEdit();
            }
        });
    }

    getUrlKey(url) {
        // Create a key for storing popup configurations (size + position) based on URL pattern
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            
            // Extract meaningful part of the path
            if (pathname.includes('weblinkToday.aspx')) {
                return 'weblink';
            } else if (pathname.includes('reports')) {
                return 'reports';
            } else if (pathname.includes('NewProspects')) {
                return 'prospects';
            } else if (pathname.includes('elead_mail')) {
                return 'mail';
            } else {
                return 'default';
            }
        } catch (error) {
            return 'default';
        }
    }

    showConversionNotification() {
        const frameContext = TrueBDCUtils.getFrameContext();

        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-popup-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #007bff, #0056b3)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        const message = frameContext.isIframe ?
            'Converting iframe content to popup window...' :
            'Converting tab to popup window...';

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="truebdc-spinner" style="width: 16px; height: 16px; border-width: 2px;"></div>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after conversion
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    showResizeTooltip() {
        const tooltip = TrueBDCUtils.createElement('div', {
            id: 'truebdc-resize-tooltip'
        }, {
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            fontSize: '13px',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '300px'
        });

        tooltip.innerHTML = `
            <div>
                <strong>💡 Tip:</strong> Resize this window to your preferred size. 
                The size will be remembered for next time!
            </div>
        `;

        document.body.appendChild(tooltip);

        // Fade in
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 100);

        // Fade out after 5 seconds
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 5000);
    }

    showErrorNotification(message) {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-error-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #dc3545, #c82333)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(220, 53, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '350px'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span>⚠️</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('Tab to Popup settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                window.removeEventListener('keydown', this.keydownHandler, true);
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            // Close popup if open
            if (this.popup && !this.popup.closed) {
                this.popup.close();
                this.popup = null;
            }

            this.isActive = false;
            
            TrueBDCUtils.log('Tab to Popup Converter destroyed');
            TrueBDCUtils.logActivity('tab_to_popup_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Tab to Popup Converter', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            savedSizesCount: this.savedSizes.size,
            popupOpen: this.popup && !this.popup.closed
        };
    }
}

// Make class globally available
window.TabToPopup = TabToPopup;
